'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Shield, Lock, Clock, AlertTriangle, Plus, X, Globe } from 'lucide-react';
import type { WebsiteSettings, SecuritySettings } from '@/lib/types';

const securitySchema = z.object({
  enableTwoFactor: z.boolean(),
  sessionTimeout: z.number().min(5, 'Session timeout must be at least 5 minutes').max(1440, 'Session timeout cannot exceed 24 hours'),
  maxLoginAttempts: z.number().min(1, 'Must allow at least 1 login attempt').max(20, 'Maximum 20 login attempts allowed'),
  lockoutDuration: z.number().min(1, 'Lockout duration must be at least 1 minute').max(1440, 'Lockout duration cannot exceed 24 hours'),
  enableCaptcha: z.boolean(),
  allowedDomains: z.array(z.string()),
});

type SecurityFormValues = z.infer<typeof securitySchema>;

interface SecuritySettingsFormProps {
  settings?: Partial<WebsiteSettings>;
  onChange: (data: Partial<SecuritySettings>) => void;
  isLoading?: boolean;
}

export function SecuritySettingsForm({ settings, onChange, isLoading }: SecuritySettingsFormProps) {
  const [newDomain, setNewDomain] = useState('');

  const form = useForm<SecurityFormValues>({
    resolver: zodResolver(securitySchema),
    defaultValues: {
      enableTwoFactor: settings?.security?.enableTwoFactor || false,
      sessionTimeout: settings?.security?.sessionTimeout || 60,
      maxLoginAttempts: settings?.security?.maxLoginAttempts || 5,
      lockoutDuration: settings?.security?.lockoutDuration || 15,
      enableCaptcha: settings?.security?.enableCaptcha || false,
      allowedDomains: settings?.security?.allowedDomains || [],
    },
    mode: 'onChange',
  });

  // Update form when settings change
  useEffect(() => {
    if (settings?.security) {
      const security = settings.security;
      form.reset({
        enableTwoFactor: security.enableTwoFactor || false,
        sessionTimeout: security.sessionTimeout || 60,
        maxLoginAttempts: security.maxLoginAttempts || 5,
        lockoutDuration: security.lockoutDuration || 15,
        enableCaptcha: security.enableCaptcha || false,
        allowedDomains: security.allowedDomains || [],
      });
    }
  }, [settings, form]);

  // Watch for form changes and notify parent
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name) {
        onChange(value as Partial<SecuritySettings>);
      }
    });
    return () => subscription.unsubscribe();
  }, [form, onChange]);

  // Add allowed domain
  const addDomain = () => {
    if (newDomain.trim()) {
      const currentDomains = form.getValues('allowedDomains');
      if (!currentDomains.includes(newDomain.trim())) {
        form.setValue('allowedDomains', [...currentDomains, newDomain.trim()]);
        setNewDomain('');
      }
    }
  };

  // Remove allowed domain
  const removeDomain = (domain: string) => {
    const currentDomains = form.getValues('allowedDomains');
    form.setValue('allowedDomains', currentDomains.filter(d => d !== domain));
  };

  // Get security level based on settings
  const getSecurityLevel = () => {
    const values = form.getValues();
    let score = 0;
    
    if (values.enableTwoFactor) score += 2;
    if (values.enableCaptcha) score += 1;
    if (values.sessionTimeout <= 30) score += 1;
    if (values.maxLoginAttempts <= 3) score += 1;
    if (values.lockoutDuration >= 30) score += 1;
    if (values.allowedDomains.length > 0) score += 1;

    if (score >= 5) return { level: 'High', color: 'bg-green-500' };
    if (score >= 3) return { level: 'Medium', color: 'bg-yellow-500' };
    return { level: 'Low', color: 'bg-red-500' };
  };

  const securityLevel = getSecurityLevel();

  return (
    <div className="space-y-6">
      <Form {...form}>
        {/* Security Level Indicator */}
        <Alert>
          <Shield className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>Current Security Level:</span>
            <Badge className={`${securityLevel.color} text-white`}>
              {securityLevel.level}
            </Badge>
          </AlertDescription>
        </Alert>

        <div className="grid gap-6 md:grid-cols-2">
          {/* Authentication Security */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-base">
                <Lock className="h-4 w-4" />
                Authentication Security
              </CardTitle>
              <CardDescription>
                Configure login and authentication settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <FormField
                control={form.control}
                name="enableTwoFactor"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Two-Factor Authentication</FormLabel>
                      <FormDescription>
                        Require 2FA for admin accounts
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        disabled={isLoading}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="enableCaptcha"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Enable CAPTCHA</FormLabel>
                      <FormDescription>
                        Show CAPTCHA on login forms
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        disabled={isLoading}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="maxLoginAttempts"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Maximum Login Attempts</FormLabel>
                    <FormControl>
                      <Input 
                        type="number"
                        min="1"
                        max="20"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 5)}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormDescription>
                      Number of failed attempts before account lockout
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="lockoutDuration"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Lockout Duration (minutes)</FormLabel>
                    <FormControl>
                      <Input 
                        type="number"
                        min="1"
                        max="1440"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 15)}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormDescription>
                      How long to lock accounts after failed attempts
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Session Management */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-base">
                <Clock className="h-4 w-4" />
                Session Management
              </CardTitle>
              <CardDescription>
                Configure user session settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="sessionTimeout"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Session Timeout (minutes)</FormLabel>
                    <FormControl>
                      <Input 
                        type="number"
                        min="5"
                        max="1440"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 60)}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormDescription>
                      Automatically log out inactive users after this time
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="space-y-2">
                <Label className="text-sm font-medium">Session Timeout Presets</Label>
                <div className="flex flex-wrap gap-2">
                  {[15, 30, 60, 120, 240, 480].map((minutes) => (
                    <Button
                      key={minutes}
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => form.setValue('sessionTimeout', minutes)}
                      disabled={isLoading}
                    >
                      {minutes < 60 ? `${minutes}m` : `${minutes / 60}h`}
                    </Button>
                  ))}
                </div>
              </div>

              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  Shorter session timeouts improve security but may inconvenience users.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </div>

        {/* Access Control */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-base">
              <Globe className="h-4 w-4" />
              Access Control
            </CardTitle>
            <CardDescription>
              Restrict access to specific domains (optional)
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="allowedDomains"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Allowed Domains</FormLabel>
                  <FormDescription>
                    Leave empty to allow all domains. Add domains to restrict access.
                  </FormDescription>
                  
                  {/* Add new domain */}
                  <div className="flex gap-2">
                    <Input
                      placeholder="example.com"
                      value={newDomain}
                      onChange={(e) => setNewDomain(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addDomain())}
                      disabled={isLoading}
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      onClick={addDomain}
                      disabled={isLoading || !newDomain.trim()}
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>

                  {/* Display current domains */}
                  {field.value.length > 0 && (
                    <div className="flex flex-wrap gap-2 mt-3">
                      {field.value.map((domain) => (
                        <Badge key={domain} variant="secondary" className="gap-1">
                          {domain}
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="h-auto p-0 hover:bg-transparent"
                            onClick={() => removeDomain(domain)}
                            disabled={isLoading}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </Badge>
                      ))}
                    </div>
                  )}
                  
                  <FormMessage />
                </FormItem>
              )}
            />

            {form.watch('allowedDomains').length === 0 && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  No domain restrictions are currently active. Users can access from any domain.
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      </Form>
    </div>
  );
}
