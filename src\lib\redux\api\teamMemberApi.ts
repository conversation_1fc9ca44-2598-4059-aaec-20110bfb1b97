import type { TeamMember } from '@/lib/types';
import { baseApi } from './baseApi';

// Define input type for creating a team member (without _id since it's generated by the backend)
type CreateTeamMemberInput = Omit<TeamMember, '_id' | 'createdAt' | 'updatedAt'>;

// Define input type for updating a team member
type UpdateTeamMemberInput = {
  _id: string;
} & Partial<Omit<TeamMember, '_id' | 'createdAt' | 'updatedAt'>>;

// Inject endpoints into the baseApi
export const teamMemberApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    // Get all team members
    getTeamMembers: builder.query<TeamMember[], void>({
      query: () => 'team-member',
      providesTags: ['TeamMembers'],
    }),

    // Get team member by ID
    getTeamMemberById: builder.query<TeamMember, string>({
      query: (id) => `team-member/${id}`,
      providesTags: (result, error, id) => [{ type: 'TeamMember', id }],
    }),

    // Add new team member
    addTeamMember: builder.mutation<TeamMember, CreateTeamMemberInput>({
      query: (newTeamMember) => ({
        url: 'team-member',
        method: 'POST',
        body: newTeamMember,
      }),
      invalidatesTags: ['TeamMembers'],
    }),

    // Update existing team member
    updateTeamMember: builder.mutation<TeamMember, UpdateTeamMemberInput>({
      query: (updateData) => ({
        url: 'team-member',
        method: 'PUT',
        body: updateData,
      }),
      invalidatesTags: (result, error, { _id }) => [
        'TeamMembers',
        { type: 'TeamMember', id: _id },
      ],
    }),

    // Delete team member
    deleteTeamMember: builder.mutation<{ success: boolean; message: string }, string>({
      query: (id) => ({
        url: `team-member?id=${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, id) => [
        'TeamMembers',
        { type: 'TeamMember', id },
      ],
    }),
  }),
});

// Export hooks for usage in UI components
export const {
  useGetTeamMembersQuery,
  useGetTeamMemberByIdQuery,
  useAddTeamMemberMutation,
  useUpdateTeamMemberMutation,
  useDeleteTeamMemberMutation,
} = teamMemberApi;
