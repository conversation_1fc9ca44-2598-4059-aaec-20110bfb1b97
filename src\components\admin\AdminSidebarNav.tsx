'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { LayoutDashboard, Box, Building, Settings, Phone, Users, UserCheck, Mail } from 'lucide-react'; // Added Mail icon

const navItems = [
  { href: '/admin', label: 'Dashboard', icon: LayoutDashboard },
  { href: '/admin/solutions', label: 'Solar Solutions', icon: Box },
  { href: '/admin/companies', label: 'Companies', icon: Building },
  { href: '/admin/team-member', label: 'Team Members', icon: UserCheck },
  { href: '/admin/subscriptions', label: 'Subscriptions', icon: Mail }, // New subscription item
  { href: '/admin/contact-settings', label: 'Contact Settings', icon: Phone },
  { href: '/admin/users', label: 'Manage Users', icon: Users },
  // { href: '/admin/settings', label: 'Settings', icon: Settings },
];

interface AdminSidebarNavProps {
  className?: string;
  onLinkClick?: () => void;
}

export function AdminSidebarNav({ className, onLinkClick }: AdminSidebarNavProps) {
  const pathname = usePathname();

  return (
    <nav className={cn("flex flex-col gap-2 text-sm font-medium", className)}>
      {navItems.map((item) => {
        const Icon = item.icon;
        const isActive = pathname === item.href || (item.href !== '/admin' && pathname.startsWith(item.href));
        return (
          <Link
            key={item.href}
            href={item.href}
            onClick={onLinkClick}
            className={cn(
              'flex items-center gap-3 rounded-lg px-3 py-2 transition-all hover:text-primary',
              isActive ? 'bg-muted text-primary' : 'text-muted-foreground'
            )}
          >
            <Icon className="h-4 w-4" />
            {item.label}
          </Link>
        );
      })}
    </nav>
  );
}
