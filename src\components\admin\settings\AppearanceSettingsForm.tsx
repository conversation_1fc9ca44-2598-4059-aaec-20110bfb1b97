'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Palette, Image, Code, Upload, X, Eye } from 'lucide-react';
import type { WebsiteSettings, AppearanceSettings } from '@/lib/types';

const appearanceSchema = z.object({
  primaryColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Please enter a valid hex color'),
  secondaryColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Please enter a valid hex color'),
  accentColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Please enter a valid hex color'),
  logoUrl: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  faviconUrl: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  customCSS: z.string().optional(),
});

type AppearanceFormValues = z.infer<typeof appearanceSchema>;

interface AppearanceSettingsFormProps {
  settings?: Partial<WebsiteSettings>;
  onChange: (data: Partial<AppearanceSettings>) => void;
  isLoading?: boolean;
}

// Predefined color themes
const colorThemes = [
  {
    name: 'Default (Solar Blue)',
    primary: '#7EC4CF',
    secondary: '#FFB347',
    accent: '#4A90E2',
  },
  {
    name: 'Ocean',
    primary: '#0077BE',
    secondary: '#00A8CC',
    accent: '#FFD700',
  },
  {
    name: 'Forest',
    primary: '#228B22',
    secondary: '#32CD32',
    accent: '#FF6347',
  },
  {
    name: 'Sunset',
    primary: '#FF6B35',
    secondary: '#F7931E',
    accent: '#FFD23F',
  },
  {
    name: 'Professional',
    primary: '#2C3E50',
    secondary: '#3498DB',
    accent: '#E74C3C',
  },
];

export function AppearanceSettingsForm({ settings, onChange, isLoading }: AppearanceSettingsFormProps) {
  const [previewColors, setPreviewColors] = useState({
    primary: '#7EC4CF',
    secondary: '#FFB347',
    accent: '#4A90E2',
  });

  const form = useForm<AppearanceFormValues>({
    resolver: zodResolver(appearanceSchema),
    defaultValues: {
      primaryColor: settings?.appearance?.primaryColor || '#7EC4CF',
      secondaryColor: settings?.appearance?.secondaryColor || '#FFB347',
      accentColor: settings?.appearance?.accentColor || '#4A90E2',
      logoUrl: settings?.appearance?.logoUrl || '',
      faviconUrl: settings?.appearance?.faviconUrl || '',
      customCSS: settings?.appearance?.customCSS || '',
    },
    mode: 'onChange',
  });

  // Update form when settings change
  useEffect(() => {
    if (settings?.appearance) {
      const appearance = settings.appearance;
      form.reset({
        primaryColor: appearance.primaryColor || '#7EC4CF',
        secondaryColor: appearance.secondaryColor || '#FFB347',
        accentColor: appearance.accentColor || '#4A90E2',
        logoUrl: appearance.logoUrl || '',
        faviconUrl: appearance.faviconUrl || '',
        customCSS: appearance.customCSS || '',
      });
      
      setPreviewColors({
        primary: appearance.primaryColor || '#7EC4CF',
        secondary: appearance.secondaryColor || '#FFB347',
        accent: appearance.accentColor || '#4A90E2',
      });
    }
  }, [settings, form]);

  // Watch for form changes and notify parent
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name) {
        onChange(value as Partial<AppearanceSettings>);
        
        // Update preview colors
        if (name === 'primaryColor' || name === 'secondaryColor' || name === 'accentColor') {
          setPreviewColors(prev => ({
            ...prev,
            [name.replace('Color', '')]: value[name as keyof AppearanceFormValues] as string,
          }));
        }
      }
    });
    return () => subscription.unsubscribe();
  }, [form, onChange]);

  // Apply color theme
  const applyTheme = (theme: typeof colorThemes[0]) => {
    form.setValue('primaryColor', theme.primary);
    form.setValue('secondaryColor', theme.secondary);
    form.setValue('accentColor', theme.accent);
  };

  return (
    <div className="space-y-6">
      <Form {...form}>
        <div className="grid gap-6 md:grid-cols-2">
          {/* Color Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-base">
                <Palette className="h-4 w-4" />
                Color Scheme
              </CardTitle>
              <CardDescription>
                Customize your website's color palette
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Quick Theme Selection */}
              <div>
                <Label className="text-sm font-medium mb-3 block">Quick Themes</Label>
                <div className="grid grid-cols-1 gap-2">
                  {colorThemes.map((theme) => (
                    <Button
                      key={theme.name}
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => applyTheme(theme)}
                      className="justify-start gap-3 h-auto p-3"
                      disabled={isLoading}
                    >
                      <div className="flex gap-1">
                        <div 
                          className="w-4 h-4 rounded-full border"
                          style={{ backgroundColor: theme.primary }}
                        />
                        <div 
                          className="w-4 h-4 rounded-full border"
                          style={{ backgroundColor: theme.secondary }}
                        />
                        <div 
                          className="w-4 h-4 rounded-full border"
                          style={{ backgroundColor: theme.accent }}
                        />
                      </div>
                      <span className="text-sm">{theme.name}</span>
                    </Button>
                  ))}
                </div>
              </div>

              {/* Individual Color Pickers */}
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="primaryColor"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Primary Color</FormLabel>
                      <FormControl>
                        <div className="flex gap-2">
                          <Input 
                            {...field}
                            disabled={isLoading}
                            className="flex-1"
                          />
                          <div className="relative">
                            <input
                              type="color"
                              value={field.value}
                              onChange={(e) => field.onChange(e.target.value)}
                              className="w-12 h-10 rounded border cursor-pointer"
                              disabled={isLoading}
                            />
                          </div>
                        </div>
                      </FormControl>
                      <FormDescription>
                        Main brand color used for buttons and highlights
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="secondaryColor"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Secondary Color</FormLabel>
                      <FormControl>
                        <div className="flex gap-2">
                          <Input 
                            {...field}
                            disabled={isLoading}
                            className="flex-1"
                          />
                          <div className="relative">
                            <input
                              type="color"
                              value={field.value}
                              onChange={(e) => field.onChange(e.target.value)}
                              className="w-12 h-10 rounded border cursor-pointer"
                              disabled={isLoading}
                            />
                          </div>
                        </div>
                      </FormControl>
                      <FormDescription>
                        Secondary color for accents and highlights
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="accentColor"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Accent Color</FormLabel>
                      <FormControl>
                        <div className="flex gap-2">
                          <Input 
                            {...field}
                            disabled={isLoading}
                            className="flex-1"
                          />
                          <div className="relative">
                            <input
                              type="color"
                              value={field.value}
                              onChange={(e) => field.onChange(e.target.value)}
                              className="w-12 h-10 rounded border cursor-pointer"
                              disabled={isLoading}
                            />
                          </div>
                        </div>
                      </FormControl>
                      <FormDescription>
                        Accent color for special elements
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Color Preview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-base">
                <Eye className="h-4 w-4" />
                Color Preview
              </CardTitle>
              <CardDescription>
                See how your colors will look
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="p-4 rounded-lg border" style={{ backgroundColor: previewColors.primary + '20' }}>
                  <Button 
                    style={{ backgroundColor: previewColors.primary }}
                    className="text-white"
                    disabled
                  >
                    Primary Button
                  </Button>
                </div>
                
                <div className="p-4 rounded-lg border" style={{ backgroundColor: previewColors.secondary + '20' }}>
                  <Button 
                    style={{ backgroundColor: previewColors.secondary }}
                    className="text-white"
                    disabled
                  >
                    Secondary Button
                  </Button>
                </div>
                
                <div className="p-4 rounded-lg border" style={{ backgroundColor: previewColors.accent + '20' }}>
                  <Button 
                    style={{ backgroundColor: previewColors.accent }}
                    className="text-white"
                    disabled
                  >
                    Accent Button
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Brand Assets */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-base">
              <Image className="h-4 w-4" />
              Brand Assets
            </CardTitle>
            <CardDescription>
              Upload or link to your logo and favicon
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="logoUrl"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Logo URL</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="https://example.com/logo.png"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormDescription>
                      URL to your website logo (recommended: 200x60px)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="faviconUrl"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Favicon URL</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="https://example.com/favicon.ico"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormDescription>
                      URL to your favicon (recommended: 32x32px)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        {/* Custom CSS */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-base">
              <Code className="h-4 w-4" />
              Custom CSS
            </CardTitle>
            <CardDescription>
              Add custom CSS to further customize your website's appearance
            </CardDescription>
          </CardHeader>
          <CardContent>
            <FormField
              control={form.control}
              name="customCSS"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Textarea 
                      placeholder="/* Add your custom CSS here */
.custom-class {
  color: #333;
  font-size: 16px;
}"
                      className="min-h-[150px] font-mono text-sm"
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormDescription>
                    Custom CSS will be applied site-wide. Use with caution.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>
      </Form>
    </div>
  );
}
