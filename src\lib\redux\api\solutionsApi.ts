import type { SolarSolution } from '@/lib/types';
import { baseApi } from './baseApi';

// Define input type for creating a solution (without _id since it's generated by the backend)
type CreateSolutionInput = Omit<SolarSolution, '_id'>;

// Define input type for updating a solution
type UpdateSolutionInput = {
  id: string;
} & Partial<Omit<SolarSolution, '_id'>>;

// Inject endpoints into the baseApi
export const solutionsApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    // Get all solutions
    getSolutions: builder.query<SolarSolution[], void>({
      query: () => 'solutions',
      providesTags: ['Solutions'],
    }),

    // Get solution by ID
    getSolutionById: builder.query<SolarSolution, string>({
      query: (id) => `solutions/${id}`,
      providesTags: (result, error, id) => [{ type: 'Solution', id }],
    }),

    // Add new solution
    addSolution: builder.mutation<SolarSolution, CreateSolutionInput>({
      query: (newSolution) => ({
        url: 'solutions',
        method: 'POST',
        body: newSolution,
      }),
      invalidatesTags: ['Solutions'],
    }),

    // Update existing solution
    updateSolution: builder.mutation<SolarSolution, UpdateSolutionInput>({
      query: ({ id, ...updateData }) => ({
        url: `solutions/${id}`,
        method: 'PUT',
        body: updateData,
      }),
      invalidatesTags: (result, error, { id }) => [
        'Solutions',
        { type: 'Solution', id },
      ],
    }),

    // Delete solution (bonus endpoint you might need)
    deleteSolution: builder.mutation<{ success: boolean; message: string }, string>({
      query: (id) => ({
        url: `solutions/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, id) => [
        'Solutions',
        { type: 'Solution', id },
      ],
    }),
  }),
});

// Export hooks for usage in UI components
export const {
  useGetSolutionsQuery,
  useGetSolutionByIdQuery,
  useAddSolutionMutation,
  useUpdateSolutionMutation,
  useDeleteSolutionMutation,
} = solutionsApi;